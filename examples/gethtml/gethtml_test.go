package gethtml

import (
	"context"
	"os"
	"path/filepath"
	"testing"
)

type mockFetcher struct{}

func (m *mockFetcher) FetchHTML(ctx context.Context, url string) (string, error) {
	return "<html>test</html>", nil
}
func (m *mockFetcher) SaveHTML(ctx context.Context, content, filename string) error {
	return nil
}

func TestHTMLFetchAndSaveToolIntegration(t *testing.T) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("获取当前目录失败: %v", err)
	}

	// 创建真实的HTTPFetcher
	logger := &silentLogger{}
	fetcher := NewHTTPFetcher(currentDir, logger)

	tool := NewHTMLFetchAndSaveTool(fetcher)
	args := map[string]interface{}{
		"url":      "https://httpbin.org/html", // 使用一个简单的测试URL
		"filename": "test.html",
	}

	result, err := tool.Execute(context.Background(), args)
	if err != nil {
		t.Fatalf("工具调用失败: %v", err)
	}

	if result == nil {
		t.Fatalf("工具返回结果为空")
	}

	// 验证文件是否被创建
	filePath := filepath.Join(currentDir, "test.html")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Fatalf("文件未被创建: %s", filePath)
	}

	t.Logf("文件成功创建: %s", filePath)
}
